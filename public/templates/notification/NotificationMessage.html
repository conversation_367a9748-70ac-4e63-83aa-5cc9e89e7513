<div
    id="notification_con_${conversationId}_mes${messageId}_sen${senderId}"
    class="max-w-md w-full bg-white shadow-lg rounded-lg flex p-4 relative"
>
    <img class="h-12 w-12 rounded-full" src="${senderImage}" alt="User Image" />
    <div class="ml-4 flex-1">
        <h2 class="text-lg font-semibold">${senderName}</h2>
        <p class="text-gray-600">${messageText}</p>
    </div>

    <!-- <a href="#con_${conversationId}_sender_${senderId}_receiver_${receiverId}_message_${messageId}" class="text-blue-500 hover:underline ml-4 mt-7">Reply</a> -->
    <button id="gotToConversation_${conversationId}" class="text-blue-500 hover:underline ml-4 mt-7 goToConversation" data-conversation_id="${conversationId}" data-message_id="con_${conversationId}_sender_${senderId}_receiver_${receiverId}_message_${messageId}">Reply</button>
    <!-- <button
        id="gotToConversation_${conversationId}"
        class="text-blue-500 hover:underline ml-4 mt-7 gotToConversation"
        data-conversation_id="${conversationId}"
    >
        <a
            href="#con_${conversationId}_sender_${senderId}_receiver_${receiverId}_message_${messageId}"
            class="text-blue-500 hover:underline ml-4 mt-7"
            >Reply</a
        >
    </button> -->

    <button
        onclick="closeNotification('con_${conversationId}_mes${messageId}_sen${senderId}')"
        class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
    >
        <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
        >
            <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
            />
        </svg>
    </button>
</div>
