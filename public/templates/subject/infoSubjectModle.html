<div id="InfoSubject_${id}" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="relative z-10" aria-labelledby="modleInfoSubject_${id}" role="dialog" aria-modal="true">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

        <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-8/12">
                    <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                        <button id="openCreatSubjectUser_${id}" onclick="showCreateSubjectUserModal('${id}')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            Create
                        </button>
                        <div class="sm:flex sm:items-start w-full">
                            <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-center w-full">
                                <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">
                                    Info Subject
                                </h3>

                                <table id="subjectUserTable_${id}" style="width: 100%" class="w-full border-collapse border border-gray-300">
                                    <thead>
                                        <tr>
                                            <th class="px-4 py-2 text-sm font-semibold text-gray-900 bg-gray-100">
                                                ID
                                            </th>
                                            <th class="px-4 py-2 text-sm font-semibold text-gray-900 bg-gray-100">
                                                Name Subject
                                            </th>
                                            <th class="px-4 py-2 text-sm font-semibold text-gray-900 bg-gray-100">
                                                Name User
                                            </th>
                                            <th class="px-4 py-2 text-sm font-semibold text-gray-900 bg-gray-100">
                                                Mark
                                            </th>
                                            <th class="px-4 py-2 text-sm font-semibold text-gray-900 bg-gray-100 text-center">
                                                Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="tbodySubjectUser_${id}" class="bg-white divide-y divide-gray-200 full-width"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                        <button type="button" onclick="closeModalInfoSubject(${id})" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
