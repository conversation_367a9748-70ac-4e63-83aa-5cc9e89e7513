<li id="user_${userId}" class="showConversation flex justify-between gap-x-6 py-5" data-conversation_id="${conversationId}">
    <div class="flex min-w-0 gap-x-4">
        <img class="h-12 w-12 flex-none rounded-full bg-gray-50" src="/public/imageProfile/${userImage}" alt="">
        <div class="min-w-0 flex-auto">
            <p class="text-sm font-semibold leading-6 text-gray-900">${userName}</p>
            <p class="mt-1 truncate text-xs leading-5 text-gray-500">${userEmail}</p>
        </div>
    </div>
    <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end">
        <p class="text-sm leading-6 text-gray-900">Co-Founder / CEO</p>
        <p class="text-xs status" id="user-status-${userId}" data-last-seen="${lastSeenAt}">
            ${isOnlinePlaceholder}
        </p>
    </div>
</li>
